# 🤖 **CHI TIẾT SPECIFICATIONS TỪNG AGENT**

## 🎯 **AGENT 1: DOMAIN CLASSIFICATION AGENT**

### **Core Responsibilities:**
- Analyze user prompt để identify primary và secondary domains
- Extract explicit và implicit requirements
- Determine project complexity và resource needs
- Map stakeholders và success criteria

### **Input Schema:**
```json
{
  "user_prompt": "string",
  "user_profile": "object (optional)",
  "duration_preference": "string",
  "additional_context": "object (optional)"
}
```

### **Output Schema:**
```json
{
  "analysis_result": {
    "primary_domain": "string",
    "sub_domains": ["array of strings"],
    "complexity_level": "beginner|intermediate|advanced|expert",
    "confidence_score": "float (0-1)",
    "extracted_requirements": {
      "functional": ["array"],
      "non_functional": ["array"],
      "technical": ["array"]
    },
    "constraints": {
      "time": "string",
      "budget": "low|medium|high|unlimited",
      "team_size": "solo|small|medium|large",
      "technical_expertise": "string"
    },
    "stakeholders": ["array"],
    "success_metrics": ["array"],
    "risk_factors": ["array"]
  }
}
```

### **AI Configuration:**
- **Primary Model:** GPT-4 hoặc Claude-3-Sonnet
- **Temperature:** 0.1 (high consistency needed)
- **Max Tokens:** 1500
- **System Prompt Focus:** Domain expertise, requirement extraction

---

## 🏗️ **AGENT 2: STRUCTURE OPTIMIZATION AGENT**

### **Core Responsibilities:**
- Design optimal milestone breakdown structure
- Determine task distribution per milestone
- Establish dependencies và critical path
- Optimize workload balance

### **Input Schema:**
```json
{
  "domain_analysis": "object from Agent 1",
  "best_practices_db": "object",
  "historical_patterns": "array",
  "user_preferences": "object"
}
```

### **Output Schema:**
```json
{
  "optimized_structure": {
    "total_milestones": "integer (3-7)",
    "milestones": [
      {
        "milestone_id": "string",
        "name": "string (7-10 words)",
        "position": "integer",
        "estimated_duration": "string",
        "dependencies": ["array of milestone_ids"],
        "critical_path": "boolean",
        "task_count": "integer (3-7)",
        "complexity_weight": "float (0-1)",
        "success_criteria": "string"
      }
    ],
    "dependency_graph": "object",
    "critical_path_analysis": "object",
    "optimization_score": "float (0-1)",
    "rationale": "string"
  }
}
```

### **AI Configuration:**
- **Primary Model:** Claude-3 (excellent structured thinking)
- **Temperature:** 0.2
- **Max Tokens:** 2000
- **System Prompt Focus:** Project structure, logical flow

---

## ✍️ **AGENT 3: CONTENT GENERATION AGENT**

### **Core Responsibilities:**
- Generate compelling names cho milestones/tasks/subtasks
- Create detailed, actionable descriptions
- Ensure language consistency và clarity
- Optimize content cho user engagement

### **Input Schema:**
```json
{
  "structure": "object from Agent 2",
  "domain_context": "object from Agent 1",
  "content_guidelines": "object",
  "language_preference": "string",
  "tone_preference": "professional|casual|motivational"
}
```

### **Output Schema:**
```json
{
  "detailed_content": {
    "milestones": [
      {
        "milestone_id": "string",
        "name": "string (7-10 words)",
        "description": "string (50-100 words)",
        "success_message": "string",
        "tasks": [
          {
            "task_id": "string",
            "name": "string (~15 words)",
            "description": "string (30-60 words)",
            "acceptance_criteria": "string",
            "subtasks": [
              {
                "subtask_id": "string",
                "name": "string (10-15 words)",
                "description": "string (25-40 words)",
                "expected_outcome": "string"
              }
            ]
          }
        ]
      }
    ]
  },
  "content_metrics": {
    "clarity_score": "float (0-1)",
    "actionability_score": "float (0-1)",
    "engagement_score": "float (0-1)"
  }
}
```

### **AI Configuration:**
- **Primary Model:** GPT-4 (superior content generation)
- **Temperature:** 0.4 (balance creativity và consistency)
- **Max Tokens:** 4000
- **System Prompt Focus:** Clear communication, actionable language

---

## ⏰ **AGENT 4: TIMELINE OPTIMIZATION AGENT**

### **Core Responsibilities:**
- Calculate realistic timelines cho all components
- Optimize resource allocation across time
- Identify scheduling conflicts và bottlenecks
- Suggest parallel execution opportunities

### **Input Schema:**
```json
{
  "structure_with_content": "object from Agent 3",
  "domain_context": "object from Agent 1",
  "resource_constraints": "object",
  "historical_benchmarks": "object",
  "calendar_constraints": "object"
}
```

### **Output Schema:**
```json
{
  "timeline_optimization": {
    "total_duration": "string",
    "start_date": "date",
    "end_date": "date",
    "milestones": [
      {
        "milestone_id": "string",
        "start_date": "date",
        "end_date": "date",
        "duration": "string",
        "buffer_time": "string",
        "tasks": [
          {
            "task_id": "string",
            "start_date": "date",
            "end_date": "date",
            "duration_days": "integer",
            "effort_hours": "integer",
            "can_parallel": "boolean",
            "parallel_with": ["array of task_ids"],
            "resource_intensity": "low|medium|high"
          }
        ]
      }
    ],
    "critical_path": ["array of milestone/task ids"],
    "parallel_opportunities": [
      {
        "tasks": ["array"],
        "time_saved": "string",
        "risk_level": "low|medium|high"
      }
    ],
    "bottlenecks": ["array"],
    "risk_factors": [
      {
        "item": "string",
        "risk_type": "string",
        "probability": "float (0-1)",
        "impact": "low|medium|high",
        "mitigation": "string"
      }
    ]
  }
}
```

### **AI Configuration:**
- **Primary Model:** Claude-3 (strong logical reasoning)
- **Temperature:** 0.1 (precision needed)
- **Max Tokens:** 3000
- **System Prompt Focus:** Project management, scheduling optimization

---

## ✅ **AGENT 5: VALIDATION AGENT**

### **Core Responsibilities:**
- Validate plan completeness và consistency
- Check logical flow và dependencies
- Verify feasibility constraints
- Ensure alignment với original requirements

### **Input Schema:**
```json
{
  "complete_plan": "object from Agent 4",
  "original_requirements": "object from Agent 1",
  "validation_rules": "object",
  "quality_standards": "object"
}
```

### **Output Schema:**
```json
{
  "validation_results": {
    "overall_score": "float (0-1)",
    "completeness_score": "float (0-1)",
    "consistency_score": "float (0-1)",
    "feasibility_score": "float (0-1)",
    "requirement_alignment": "float (0-1)",
    "issues_found": [
      {
        "issue_id": "string",
        "type": "missing|inconsistent|infeasible|misaligned",
        "severity": "low|medium|high|critical",
        "location": "string",
        "description": "string",
        "suggested_fix": "string",
        "auto_fixable": "boolean"
      }
    ],
    "improvements_suggested": [
      {
        "area": "string",
        "suggestion": "string",
        "impact": "low|medium|high",
        "effort_required": "low|medium|high"
      }
    ],
    "quality_gates": {
      "structure_quality": "pass|fail",
      "content_quality": "pass|fail",
      "timeline_quality": "pass|fail",
      "overall_quality": "pass|fail"
    }
  }
}
```

### **AI Configuration:**
- **Primary Model:** GPT-4 (strong analytical capabilities)
- **Temperature:** 0.0 (maximum consistency)
- **Max Tokens:** 2500
- **System Prompt Focus:** Critical analysis, quality assurance

---

## 🌟 **AGENT 6: QUALITY ENHANCEMENT AGENT**

### **Core Responsibilities:**
- Apply final improvements based on validation
- Enhance language clarity và engagement
- Add motivational elements
- Optimize overall user experience

### **Input Schema:**
```json
{
  "validated_plan": "object from Agent 5",
  "validation_feedback": "object from Agent 5",
  "user_preferences": "object",
  "enhancement_guidelines": "object"
}
```

### **Output Schema:**
```json
{
  "enhanced_plan": {
    // Final polished plan structure
    "plan_metadata": {
      "quality_score": "float (0-1)",
      "enhancement_level": "basic|standard|premium",
      "personalization_score": "float (0-1)"
    }
  },
  "enhancements_applied": [
    {
      "type": "clarity|motivation|structure|personalization",
      "location": "string",
      "original": "string",
      "enhanced": "string",
      "rationale": "string"
    }
  ],
  "final_metrics": {
    "readability_score": "float (0-1)",
    "engagement_score": "float (0-1)",
    "actionability_score": "float (0-1)",
    "motivation_score": "float (0-1)"
  }
}
```

### **AI Configuration:**
- **Primary Model:** GPT-4 (best creative enhancement)
- **Temperature:** 0.3 (balanced creativity)
- **Max Tokens:** 3500
- **System Prompt Focus:** User experience, engagement optimization

---

## 🔧 **CROSS-AGENT SPECIFICATIONS**

### **Error Handling:**
- Each agent has **retry logic** (max 3 attempts)
- **Fallback models** cho primary model failures
- **Graceful degradation** strategies
- **Error propagation** protocols

### **Quality Gates:**
- **Minimum quality thresholds** cho each agent output
- **Automatic retry** nếu quality below threshold
- **Human intervention triggers** cho critical failures

### **Performance Monitoring:**
- **Response time tracking** cho each agent
- **Quality metrics collection**
- **Cost tracking** per agent
- **Success rate monitoring**

---

*Document created: 2025-01-29*
*Status: Technical Specification*
*Version: 1.0*
