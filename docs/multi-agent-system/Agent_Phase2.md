# 📋 **STREAMLINED TASK REQUIREMENTS - MULTI-AGENT SYSTEM**

## 🎯 **OVERVIEW**

Document này mô tả chi tiết 54 tasks để chuyển đ<PERSON>i sang multi-agent system, với input/output đư<PERSON><PERSON> tích hợp trực tiếp trong từng task description.

---

## 🤖 **PHASE 2: CORE AGENTS IMPLEMENTATION**

### **Task 2.1: Implement Domain Classification Agent**
**Mục tiêu**: Tạo agent phân tích domain và extract requirements từ user input

**📥 INPUT:**
```json
{
    "user_input": "Tôi muốn tạo một ứng dụng mobile e-commerce bán quần áo",
    "duration": "3 months",
    "language": "vietnamese"
}
```

**🔧 THỰC HIỆN:**
- Create DomainClassificationAgent class inheriting BaseIgnitionAgent
- Design AI prompts cho domain analysis
- Implement requirement extraction algorithms
- Add confidence scoring system

**📤 OUTPUT (Added to state["domain_analysis"]):**
```json
{
    "primary_domain": "mobile_app_development",
    "sub_domains": ["e_commerce", "fashion_retail", "user_experience"],
    "complexity_level": "intermediate",
    "confidence_score": 0.92,
    "extracted_requirements": {
        "functional": ["user_authentication", "product_catalog", "payment_processing"],
        "non_functional": ["mobile_responsive", "secure_payments", "scalable"],
        "technical": ["mobile_app", "backend_api", "database_design"]
    },
    "constraints": {
        "time": "3_months",
        "budget": "medium", 
        "team_size": "small_team_3_5_people"
    },
    "success_metrics": ["user_adoption_rate", "conversion_rate", "app_store_rating"],
    "stakeholders": ["end_users", "business_owners", "development_team"]
}
```

**✅ Success Criteria:** 90%+ accuracy, confidence scores correlate with quality, <10s response time
**⏱️ Duration:** 3-4 ngày

---

### **Task 2.2: Create Domain Analysis Prompts**
**Mục tiêu**: Thiết kế optimal prompts cho domain classification

**📥 INPUT:** Domain classification requirements, prompt engineering best practices

**🔧 THỰC HIỆN:**
- Design system prompt templates với role definition
- Create user prompt formatting logic
- Build few-shot examples database
- Add multi-language support

**📤 OUTPUT:**
```python
# prompts/domain_analysis.py
DOMAIN_ANALYSIS_SYSTEM_PROMPT = """
Bạn là chuyên gia phân tích domain với 10+ năm kinh nghiệm...
Nhiệm vụ: Phân tích user input và xác định:
1. Primary domain (mobile_app, web_app, data_science, etc.)
2. Sub-domains liên quan
3. Complexity level (beginner/intermediate/advanced)
...
Trả về JSON format với structure rõ ràng.
"""

def format_domain_analysis_prompt(user_input: str, duration: str) -> str:
    return f"""
    Phân tích dự án này:
    Input: "{user_input}"
    Duration: {duration}
    """

FEW_SHOT_EXAMPLES = [
    {"input": "tạo website bán hàng", "output": {...}},
    {"input": "phân tích dữ liệu khách hàng", "output": {...}}
]
```

**✅ Success Criteria:** Consistent outputs, multi-language works, A/B testing shows improvement
**⏱️ Duration:** 2-3 ngày

---

### **Task 2.3: Implement Structure Optimization Agent**
**Mục tiêu**: Tạo agent thiết kế optimal project structure

**📥 INPUT:**
```json
{
    "domain_analysis": {
        "primary_domain": "mobile_app_development",
        "complexity_level": "intermediate",
        "extracted_requirements": [...],
        "constraints": {"time": "3_months"}
    }
}
```

**🔧 THỰC HIỆN:**
- Create StructureOptimizationAgent class
- Implement milestone generation logic
- Add task distribution algorithms
- Calculate dependencies và critical path

**📤 OUTPUT (Added to state["structure_design"]):**
```json
{
    "milestone_structure": [
        {
            "milestone_id": "M1",
            "name": "Project Foundation and Planning",
            "position": 1,
            "estimated_duration": "3_weeks",
            "dependencies": [],
            "critical_path": true,
            "task_count": 5,
            "complexity_weight": 0.7,
            "tasks": [
                {
                    "task_id": "T1_1",
                    "name": "Market Research and Competitor Analysis",
                    "estimated_duration": "1_week",
                    "complexity": "medium"
                }
                // ... 4 more tasks
            ]
        }
        // ... 4 more milestones
    ],
    "dependency_graph": {"M1": [], "M2": ["M1"], "M3": ["M2"]},
    "critical_path_analysis": {
        "critical_milestones": ["M1", "M2", "M3", "M5"],
        "total_duration": "11_weeks",
        "buffer_time": "1_week"
    },
    "optimization_score": 0.89
}
```

**✅ Success Criteria:** Logical structure, balanced tasks (3-7 per milestone), accurate dependencies
**⏱️ Duration:** 4-5 ngày

---


**✅ Success Criteria:** Quality scores >0.9, enhanced readability, motivational elements added
**⏱️ Duration:** 3-4 ngày

---

## 🎯 **SUMMARY: DATA TRANSFORMATION JOURNEY**

```
"Tôi muốn tạo app e-commerce bán quần áo" 
    ↓ Domain Agent
Domain analysis JSON với detailed requirements
    ↓ Structure Agent  
5 milestones với 25 tasks framework
    ↓ Content + Timeline Agents (Parallel)
Detailed content + Realistic schedules
    ↓ Validation Agent
Quality-checked plan với issues identified
    ↓ Quality Agent
Enhanced, motivational, actionable final plan với 125 subtasks
```

**Total: 54 tasks, 12-16 weeks, transforming simple idea into world-class development plan!** 🚀
