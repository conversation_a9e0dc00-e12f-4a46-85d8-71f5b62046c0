{"name": "react-dashboard", "version": "1.2.4", "description": "React version of member team", "main": "index.js", "keywords": ["react", "reactjs", "dashboard", "dashboard-react"], "author": "Ignition Team", "homepage": "", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "compile:scss": "sass src/assets/scss/main.scss src/assets/css/main.css", "minify:scss": "sass src/assets/scss/main.scss src/assets/css/main.min.css --style compressed", "build:scss": "npm run compile:scss && npm run minify:scss && npm run map:scss"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource-variable/recursive": "^5.1.0", "@fortawesome/fontawesome-free": "6.5.1", "@iconify/react": "^5.0.2", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.16.4", "@mui/joy": "^5.0.0-beta.48", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.20", "@mui/styles": "^5.16.0", "@reduxjs/toolkit": "^2.2.7", "ajv": "8.12.0", "ajv-keywords": "5.1.0", "aos": "^2.3.4", "axios": "^1.6.7", "bootstrap": "4.6.2", "bootstrap-icons": "^1.11.3", "chart.js": "2.9.4", "classnames": "^2.5.1", "dayjs": "^1.11.12", "dhtmlx-gantt": "^9.0.12", "dhtmlx-scheduler": "^6.0.5", "js-cookie": "^3.0.5", "legion-ui": "^0.1.7", "libphonenumber-js": "^1.12.9", "matter-js": "^0.20.0", "moment": "2.29.4", "nouislider": "15.4.0", "prop-types": "^15.8.1", "query-string": "^9.0.0", "randomcolor": "^0.6.2", "react": "18.2.0", "react-chartjs-2": "2.11.2", "react-circular-menu": "^2.4.11", "react-circular-progressbar": "^2.1.0", "react-copy-to-clipboard": "5.1.0", "react-dom": "18.2.0", "react-drag-drop-files": "^2.3.10", "react-icons": "^5.2.1", "react-inline-editing": "^1.0.10", "react-phone-number-input": "^3.4.12", "react-pro-sidebar": "^1.1.0", "react-redux": "^9.1.2", "react-responsive-masonry": "^2.4.1", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "react-toastify": "^10.0.4", "reactstrap": "8.10.0", "redux": "^5.0.1", "sass": "1.69.5", "scheduler-react": "^2.0.20", "schema-utils": "4.2.0", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.5", "styled-components": "^6.1.12"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "electron": "^28.2.0", "gulp": "^5.0.0", "gulp-append-prepend": "1.0.9"}, "optionalDependencies": {"eslint-plugin-flowtype": "8.0.3", "typescript": "5.3.3"}, "overrides": {"svgo": "3.0.2", "fsevents": "2.3.2", "chokidar": "3.5.3"}}