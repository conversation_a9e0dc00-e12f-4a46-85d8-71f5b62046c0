name: CI/CD Pipeline

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Create .env file
        run: |
          echo "REACT_APP_API_URL=http://localhost:8000" > .env

      - name: Run linting
        run: npm run lint || echo "Linting issues found"

      - name: Run tests
        run: npm test -- --passWithNoTests

  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Create .env file
        run: |
          echo "REACT_APP_API_URL=${{ secrets.REACT_APP_API_URL }}" > .env.production

      - name: Build
        run: npm run build

      - name: Create build archive
        run: tar -czf build.tar.gz build

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add host key
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          scp build.tar.gz ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:~
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
            sudo mkdir -p ${{ secrets.REMOTE_DIR }}
            sudo rm -rf ${{ secrets.REMOTE_DIR }}/*
            sudo tar -xzf build.tar.gz -C ${{ secrets.REMOTE_DIR }} --strip-components=1
            rm build.tar.gz
            sudo systemctl reload nginx
            echo "Deployment completed successfully!"
          EOF
