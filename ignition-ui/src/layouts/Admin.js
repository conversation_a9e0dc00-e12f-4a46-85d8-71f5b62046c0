import React, { useEffect, useState } from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import MobileHeader from "components/Sidebar/MobileHeader";
import AdminFooter from "components/Footers/AdminFooter";
import SidebarComponent from "components/Sidebar/SidebarComponent";
import { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX } from "helpers/constants";
import { getRoutes } from "helpers/auth";
import routes from "routes/index";

//--------------------------------------------------------------------------------------------------

const AdminLayout = () => {
  const mainContent = React.useRef(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      const newIsSmartphone = window.innerWidth < 768;
      setIsSmartphone(newIsSmartphone);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate margin and width for main content
  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;

  const mainContentStyle = {
    marginLeft: leftMargin,
    transition: 'margin-left 0.3s ease',
    width: isSmartphone ? '100%' : `calc(100% - ${leftMargin})`,
    maxWidth: '100%',
    overflow: 'hidden',
    paddingTop: isSmartphone ? '70px' : '0', // Add top padding for mobile header
    minHeight: isSmartphone ? 'calc(100vh - 70px)' : '100vh'
  };

  return (
    <>
      {isSmartphone ? (<MobileHeader
        onCollapseChange={setSidebarCollapsed}
        logo={{
          innerLink: "/d/",
          imgSrc: require("../assets/main_logo.png"),
          imgAlt: "Logo",
        }} />
      ) : (
        <SidebarComponent onCollapseChange={setSidebarCollapsed}
          logo={{
            innerLink: "/d",
            imgSrc: require(sidebarCollapsed ? "../assets/fire_logo.png" : "../assets/main_logo.png"),
            imgAlt: "Logo",
          }} />
      )}
      <div className="main-content" ref={mainContent} style={{
        ...mainContentStyle,
        boxShadow: isSmartphone ? 'none' : '0 4px 20px rgba(0, 0, 0, 0.08)',
        borderRadius: isSmartphone ? '0' : '12px 0 0 0',
        backgroundColor: '#fafafa',
        minHeight: isSmartphone ? 'calc(100vh - 70px)' : '100vh',
        padding: isSmartphone ? '15px' : '20px'
      }}>
        <Routes>
          {getRoutes(routes, ADMIN_PAGE_KEY)}
          <Route path="*" element={<Navigate to="/d/" replace />} />
        </Routes>
        <AdminFooter />
      </div>
    </>
  );
};

export default AdminLayout;
