import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Card,
  CardContent,

} from '@mui/material';
// import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';
import { getHeaders } from 'helpers/functions';
import { APIURL } from 'helpers/constants';


dayjs.extend(isBetween);

const EnhancedTimeline = ({ planInfo, onTaskClick }) => {
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [tasks, setTasks] = useState([]);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);

  // Get 7 days starting from current week
  const getDays = () => {
    const startOfWeek = currentDate.startOf('week');
    return Array.from({ length: 7 }, (_, i) => startOfWeek.add(i, 'day'));
  };

  // Fetch tasks from plan
  useEffect(() => {
    if (planInfo?.milestones) {
      const allTasks = [];
      planInfo.milestones.forEach(milestone => {
        if (milestone.tasks) {
          milestone.tasks.forEach(task => {
            allTasks.push({
              ...task,
              milestone_name: milestone.name,
              milestone_id: milestone.id
            });
          });
        }
      });
      setTasks(allTasks);
    }
  }, [planInfo]);

  // Get tasks for a specific day
  const getTasksForDay = (date) => {
    return tasks.filter(task => {
      const taskStart = task.start_date ? dayjs(task.start_date) : null;
      const taskEnd = task.end_date ? dayjs(task.end_date) : null;
      const taskDeadline = task.deadline ? dayjs(task.deadline) : null;

      return (taskStart && date.isSame(taskStart, 'day')) ||
             (taskEnd && date.isSame(taskEnd, 'day')) ||
             (taskDeadline && date.isSame(taskDeadline, 'day')) ||
             (taskStart && taskEnd && date.isBetween(taskStart, taskEnd, 'day', '[]'));
    });
  };

  // Get status info for task
  const getStatusInfo = (task) => {
    const statusMap = {
      1: { label: 'Pending', color: '#FFA726' },
      2: { label: 'In Progress', color: '#42A5F5' },
      3: { label: 'Completed', color: '#66BB6A' },
      4: { label: 'On Hold', color: '#EF5350' }
    };
    return statusMap[task.status] || statusMap[1];
  };

  // Check if task spans multiple days
  // const isMultiDayTask = (task) => {
  //   const taskStart = task.start_date ? dayjs(task.start_date) : null;
  //   const taskEnd = task.end_date ? dayjs(task.end_date) : null;
  //   return taskStart && taskEnd && !taskStart.isSame(taskEnd, 'day');
  // };

  // Get task position for multi-day tasks
  // const getTaskPosition = (task, currentDay) => {
  //   const taskStart = task.start_date ? dayjs(task.start_date) : null;
  //   const taskEnd = task.end_date ? dayjs(task.end_date) : null;
  //
  //   if (!taskStart || !taskEnd) return { isStartDay: true, isEndDay: true };
  //
  //   return {
  //     isStartDay: currentDay.isSame(taskStart, 'day'),
  //     isEndDay: currentDay.isSame(taskEnd, 'day')
  //   };
  // };

  // Navigation functions
  const goToPrevious = () => setCurrentDate(currentDate.subtract(7, 'day'));
  const goToNext = () => setCurrentDate(currentDate.add(7, 'day'));
  const goToToday = () => setCurrentDate(dayjs());

  // Handle task click
  const handleTaskClick = (task) => {
    if (onTaskClick) {
      onTaskClick(task);
    }
  };

  // Handle empty time slot click for scheduling
  const handleTimeSlotClick = (date) => {
    setSelectedDate(date);
    setShowScheduleDialog(true);
  };



  // Get available tasks for scheduling (tasks without dates)
  const getUnscheduledTasks = () => {
    return tasks.filter(task => !task.start_date && !task.end_date);
  };

  // Schedule task to selected date
  const scheduleTask = async (task, date) => {
    try {
      // Simple date scheduling
      const response = await fetch(`${APIURL}/api/tasks/${task.slug}/update`, {
        method: 'PUT',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          start_date: date.format('YYYY-MM-DD'),
          end_date: date.format('YYYY-MM-DD')
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update task');
      }

      // Update local state
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id
            ? {
                ...t,
                start_date: date.format('YYYY-MM-DD'),
                end_date: date.format('YYYY-MM-DD')
              }
            : t
        )
      );

      setShowScheduleDialog(false);
      setSelectedDate(null);
    } catch (error) {
      console.error('Error scheduling task:', error);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* Header */}
      <Box sx={{ 
        mb: 3, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        flexWrap: 'wrap', 
        gap: 2 
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography 
            variant="h6" 
            sx={{ 
              fontWeight: 600, 
              color: '#333', 
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <Iconify icon="material-symbols:calendar-view-week" width={24} height={24} color={mainYellowColor} />
            Project Timeline
          </Typography>
          
          <Chip
            label={`${tasks.length} tasks`}
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600
            }}
          />


        </Box>

        {/* Navigation Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Previous week">
            <IconButton onClick={goToPrevious} size="small">
              <Iconify icon="material-symbols:chevron-left" width={20} height={20} />
            </IconButton>
          </Tooltip>
          
          <Button
            onClick={goToToday}
            variant="outlined"
            size="small"
            sx={{
              borderColor: mainYellowColor,
              color: mainYellowColor,
              '&:hover': {
                backgroundColor: `${mainYellowColor}10`,
                borderColor: mainYellowColor
              }
            }}
          >
            Today
          </Button>
          
          <Tooltip title="Next week">
            <IconButton onClick={goToNext} size="small">
              <Iconify icon="material-symbols:chevron-right" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Week Display */}
      <Typography 
        variant="h6" 
        sx={{ 
          textAlign: 'center', 
          mb: 2, 
          fontFamily: '"Recursive Variable", sans-serif',
          color: '#666'
        }}
      >
        {getDays()[0].format('MMMM YYYY')} • Week of {getDays()[0].format('MMM D')}
      </Typography>

      {/* Calendar Grid */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Days Header */}
        <Grid container spacing={1} sx={{ mb: 1, backgroundColor: '#f5f7fa', p: 1, borderRadius: '12px' }}>
          {getDays().map((day) => (
            <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
              <Paper
                elevation={0}
                sx={{
                  p: 1.5,
                  textAlign: 'center',
                  backgroundColor: day.isSame(dayjs(), 'day') ? `${mainYellowColor}15` : 'white',
                  borderRadius: '8px',
                  border: '1px solid #f0f0f0',
                  boxShadow: day.isSame(dayjs(), 'day') ? `0 0 0 1px ${mainYellowColor}40` : 'none'
                }}
              >
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: '#666',
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontWeight: 600,
                    display: 'block'
                  }}
                >
                  {day.format('ddd')}
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: day.isSame(dayjs(), 'day') ? 700 : 600,
                    color: day.isSame(dayjs(), 'day') ? mainYellowColor : '#333',
                    fontFamily: '"Recursive Variable", sans-serif'
                  }}
                >
                  {day.format('D')}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>

        {/* Tasks Grid */}
        <Grid container spacing={1} sx={{ flexGrow: 1 }}>
          {getDays().map((day) => {
            const dayTasks = getTasksForDay(day);
            return (
              <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
                <Paper
                  elevation={0}
                  onClick={() => handleTimeSlotClick(day)}
                  sx={{
                    p: 1,
                    height: '100%',
                    minHeight: '400px',
                    backgroundColor: day.isSame(dayjs(), 'day') ? '#fafafa' : '#fff',
                    borderRadius: '8px',
                    border: '1px solid #f0f0f0',
                    cursor: dayTasks.length === 0 ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: dayTasks.length === 0 ? '#f9f9f9' : undefined
                    }
                  }}
                >
                  {dayTasks.length === 0 ? (
                    <Box 
                      sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        alignItems: 'center', 
                        justifyContent: 'center',
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.8rem',
                        gap: 1
                      }}
                    >
                      <Iconify icon="material-symbols:add-circle-outline" width={24} height={24} />
                      <Typography variant="caption" sx={{ textAlign: 'center' }}>
                        Click to schedule tasks
                      </Typography>
                    </Box>
                  ) : (
                    <Stack spacing={1}>
                      {dayTasks.map((task) => {
                        const statusInfo = getStatusInfo(task);
                        // const isMultiDay = isMultiDayTask(task);
                        // const { isStartDay, isEndDay } = getTaskPosition(task, day);

                        return (
                          <Paper
                            key={task.id}
                            elevation={0}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTaskClick(task);
                            }}
                            sx={{
                              p: 1,
                              backgroundColor: `${statusInfo.color}08`,
                              border: `1px solid ${statusInfo.color}20`,
                              borderRadius: '4px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                backgroundColor: `${statusInfo.color}15`,
                                transform: 'translateY(-1px)',
                                boxShadow: `0 2px 8px ${statusInfo.color}20`
                              }
                            }}
                          >
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                fontWeight: 600,
                                color: '#333',
                                fontFamily: '"Recursive Variable", sans-serif',
                                fontSize: '0.8rem',
                                lineHeight: 1.2,
                                mb: 0.5
                              }}
                            >
                              {task.name}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <Chip
                                label={statusInfo.label}
                                size="small"
                                sx={{
                                  backgroundColor: statusInfo.color,
                                  color: 'white',
                                  fontSize: '0.65rem',
                                  height: '18px',
                                  fontWeight: 600
                                }}
                              />
                              
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  color: '#666',
                                  fontSize: '0.7rem',
                                  fontFamily: '"Recursive Variable", sans-serif'
                                }}
                              >
                                {task.milestone_name}
                              </Typography>
                            </Box>
                          </Paper>
                        );
                      })}
                    </Stack>
                  )}
                </Paper>
              </Grid>
            );
          })}
        </Grid>
      </Box>

      {/* Simple Task Scheduling Dialog */}
      <Dialog
        open={showScheduleDialog}
        onClose={() => setShowScheduleDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Schedule Task for {selectedDate?.format('MMMM D, YYYY')}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
            Select a task to schedule for this date:
          </Typography>

          <Stack spacing={1}>
            {getUnscheduledTasks().map((task) => (
              <Card
                key={task.id}
                elevation={0}
                sx={{
                  border: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: '#f9f9f9'
                  }
                }}
                onClick={() => scheduleTask(task, selectedDate)}
              >
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {task.name}
                  </Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>
                    {task.milestone_name}
                  </Typography>
                </CardContent>
              </Card>
            ))}

            {getUnscheduledTasks().length === 0 && (
              <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', py: 2 }}>
                No unscheduled tasks available
              </Typography>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowScheduleDialog(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>



    </Box>
  );
};

export default EnhancedTimeline;
